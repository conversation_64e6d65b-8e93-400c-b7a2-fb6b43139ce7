{"data_mtime": **********, "dep_lines": [5, 6, 7, 8, 9, 1, 5, 6, 7, 8, 9, 1, 1, 1, 1, 4, 3], "dep_prios": [10, 10, 10, 10, 10, 5, 20, 20, 20, 20, 20, 5, 30, 30, 30, 5, 5], "dependencies": ["Account.models", "Membership.models", "Shield.models", "Alert.models", "Ticket.models", "datetime", "Account", "Membership", "Shield", "<PERSON><PERSON>", "Ticket", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "8f9f27c61a9a43ec6763bcb17e21274226dca051", "id": "Shield.serializers", "ignore_all": true, "interface_hash": "3c742956fb751283ef0582d64a7a49ca361b798a", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\MAS_SEGUROS\\local_project\\mas_seguros_backend-main\\Shield\\serializers.py", "plugin_data": null, "size": 19571, "suppressed": ["django.contrib.auth.models", "rest_framework"], "version_id": "1.15.0"}